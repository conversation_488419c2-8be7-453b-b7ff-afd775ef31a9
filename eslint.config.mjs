/* eslint-disable max-lines -- config file */
import eslintJs from '@eslint/js';
import eslintCommentsPlugin from '@eslint-community/eslint-plugin-eslint-comments/configs';
import stylistic from '@stylistic/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import {createTypeScriptImportResolver} from 'eslint-import-resolver-typescript';
import banPlugin from 'eslint-plugin-ban';
import importXPlugin from 'eslint-plugin-import-x';
import jsxA11yPlugin from 'eslint-plugin-jsx-a11y';
import noLoopsPlugin from 'eslint-plugin-no-loops';
import reactPlugin from 'eslint-plugin-react';
import reactCompilerPlugin from 'eslint-plugin-react-compiler';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import reactHooksAddonsPlugin from 'eslint-plugin-react-hooks-addons';
import sonarjsPlugin from 'eslint-plugin-sonarjs';
import sortPlugin from 'eslint-plugin-sort';
import testingLibraryPlugin from 'eslint-plugin-testing-library';
import unicornPlugin from 'eslint-plugin-unicorn';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';
import {config, configs} from 'typescript-eslint';
import {
  booleanNameConvention,
  booleanNameExceptions,
  booleanNamePrefixes,
} from './scripts/eslint/boolean-naming.cjs';

export default config(
  {
    // Global ignores - applied to all configurations
    ignores: [
      // Dot directories
      '.*/**', // All dot directories (includes .git, .github, .firebase, .expo, etc.)
      // Build and generated directories
      '**/dist/**', // All dist directories
      '**/dist-*/**', // All dist-* directories (like dist-dev, dist-prod)
      'coverage/**', // Test coverage reports
      // Dependencies
      '**/node_modules/**', // All node_modules directories
      // Native platform directories
      'android/**', // Android platform code
      'ios/**', // iOS platform code
      // Other non-source directories
      '**/lib/**', // Generated libraries
      '**/__mocks__/**', // Jest mocks
      '**/patches/**', // Patches directory
    ],
  },
  // Base Configuration (applies globally unless overridden)
  eslintJs.configs.recommended,
  configs.recommendedTypeChecked,
  configs.stylisticTypeChecked,
  configs.strict,
  stylistic.configs.customize({
    endOfLine: 'lf',
    semi: true,
    singleQuote: true,
    bracketSpacing: false,
    jsxSingleQuote: true,
    bracketSameLine: false,
    arrowParens: 'avoid',
    tabWidth: 2,
    trailingComma: 'all',
    printWidth: 100,
    quoteProps: 'consistent',
  }),
  // Import plugin recommended configs
  reactPlugin.configs.flat.recommended,
  reactHooksAddonsPlugin.configs.recommended,
  reactHooksPlugin.configs['recommended-latest'],
  jsxA11yPlugin.flatConfigs.recommended,
  importXPlugin.flatConfigs.recommended,
  importXPlugin.flatConfigs.typescript,
  sortPlugin.configs['flat/recommended'],
  unicornPlugin.configs.recommended,
  eslintCommentsPlugin.recommended,
  reactCompilerPlugin.configs.recommended,
  sonarjsPlugin.configs.recommended,
  // Main configuration object
  {
    files: ['**/*.{js,mjs,cjs,ts,tsx}'],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        projectService: true,
        project: [
          './tsconfig.json',
          './functions/tsconfig.json',
          './functions/report-generation/tsconfig.json',
          './web-sign-up/tsconfig.json',
        ],
        tsconfigRootDir: import.meta.dirname,
        ecmaFeatures: {jsx: true},
        warnOnUnsupportedTypeScriptVersion: true,
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2025,
      },
    },
    // We only need to define plugins that aren't already included in the configs above
    plugins: {
      'no-loops': noLoopsPlugin,
      'ban': banPlugin,
      'testing-library': testingLibraryPlugin,
      'unused-imports': unusedImports,
    },
    settings: {
      'react': {version: 'detect'},
      'import-x/resolver-next': [
        createTypeScriptImportResolver({
          alwaysTryTypes: true,
          bun: true,
        }),
      ],
      'import-x/ignore': ['react-native'],
      'import-x/extensions': ['.js', '.cjs', '.mjs'],
      'import-x/parsers': {'typescript-eslint': ['.ts', '.tsx']},
    },
    linterOptions: {
      reportUnusedDisableDirectives: true,
    },
    rules: {
      // --- Stylistic & Formatting Rules ---
      // Base formatting options
      'quote-props': ['warn', 'consistent-as-needed'],
      'quotes': 'off',
      '@stylistic/quotes': ['warn', 'single', {avoidEscape: true}],
      'jsx-quotes': 'off',
      '@stylistic/jsx-quotes': ['warn', 'prefer-single'],
      '@stylistic/jsx-one-expression-per-line': 'off', // ['warn', {allow: 'non-jsx'}],
      '@stylistic/block-spacing': ['warn', 'never'],
      'curly': ['warn', 'multi-line'],

      // Object formatting
      'object-curly-spacing': 'off',
      '@stylistic/object-curly-spacing': ['warn', 'never'],
      '@stylistic/object-curly-newline': ['warn', {ObjectPattern: {multiline: true, consistent: true}}],
      'object-property-newline': ['warn', {allowAllPropertiesOnSameLine: true}],

      // Spacing and line breaks
      'function-call-argument-newline': ['warn', 'consistent'],
      'arrow-parens': 'off',
      '@stylistic/arrow-parens': ['warn', 'as-needed'],
      'no-multiple-empty-lines': ['warn', {max: 1, maxEOF: 1}],
      'no-multi-spaces': 'warn',
      'arrow-spacing': 'warn',
      'space-infix-ops': 'warn',
      '@stylistic/operator-linebreak': ['warn', 'after', {overrides: {'?': 'before', ':': 'before'}}],
      // Additional formatting rules
      'no-bitwise': 'error',
      'comma-style': ['warn', 'last'],
      'no-redeclare': 'off', // Use TS version
      'complexity': ['warn', {max: 20}],
      '@stylistic/max-len': [
        'warn',
        {
          code: 100,
          comments: 120,
          ignoreUrls: true,
          ignoreTrailingComments: true,
          ignorePattern: String.raw`^.*eslint-(disable|enable).+|it\(|ErrorCodes|@param|@return|^\s*\[[^\]]+\]:\s*.+?;$`,
          ignoreTemplateLiterals: true,
          ignoreStrings: true,
          ignoreRegExpLiterals: true,
        },
      ],
      // --- General Code Style Rules ---
      // Code size and structure
      'max-lines-per-function': ['error', 120],
      'max-lines': ['error', {max: 900, skipBlankLines: true}],
      'func-style': ['warn', 'expression'],
      'padding-line-between-statements': ['error', {blankLine: 'never', prev: '*', next: 'import'}],

      // Modern JavaScript features
      'object-shorthand': ['warn', 'always'],
      'arrow-body-style': ['warn', 'as-needed'],
      'prefer-destructuring': 'warn',
      'prefer-arrow-callback': 'error',
      'prefer-template': 'warn',
      'one-var': ['error', 'never'],

      // Function parameter rules
      'default-param-last': 'off', // Use TS version
      '@typescript-eslint/default-param-last': ['error'],

      // Async/await rules
      'require-await': 'off', // Use TS version
      '@typescript-eslint/require-await': 'off',
      'no-return-await': 'warn',

      // Control flow and logic
      'no-nested-ternary': 'warn',
      'no-unneeded-ternary': 'warn',
      'no-else-return': 'warn',
      'no-constant-condition': 'off',

      // Object property access
      'dot-notation': 'off', // Use TS version
      '@typescript-eslint/dot-notation': ['error'],

      // Variable usage
      'no-unused-expressions': 'off', // Use TS version
      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
          allowTaggedTemplates: true,
        },
      ],
      'no-use-before-define': 'off', // Use TS version
      '@typescript-eslint/no-use-before-define': ['error', {variables: true, functions: false}],

      // Error prevention
      'no-console': 'warn',
      'no-useless-concat': 'warn',
      'no-new': 'error',
      'no-implicit-coercion': ['warn', {allow: ['!!']}],
      'no-extra-boolean-cast': 'warn',

      // Magic numbers
      'no-magic-numbers': 'off', // Use TS version
      '@typescript-eslint/no-magic-numbers': [
        'error',
        {
          ignoreEnums: true,
          ignoreArrayIndexes: true,
          ignoreDefaultValues: true,
          ignoreTypeIndexes: true,
          ignore: [0, 1, -1, 2],
        },
      ],

      // --- Additional Stylistic Rules ---
      '@stylistic/brace-style': ['warn', '1tbs', {allowSingleLine: true}],
      '@stylistic/comma-dangle': ['warn', 'always-multiline'],
      '@stylistic/comma-spacing': 'warn',
      '@stylistic/keyword-spacing': 'error',
      'func-call-spacing': 'off', // Use stylistic version
      '@stylistic/func-call-spacing': ['error'],

      // --- TypeScript-Specific Rules ---
      // --- Naming Conventions ---
      // JavaScript naming conventions
      'camelcase': ['warn', {allow: ['^_', 'content_type', 'reply_to']}],

      // TypeScript naming conventions
      '@typescript-eslint/naming-convention': [
        'warn',
        // Property naming rules
        {
          selector: 'property',
          format: ['camelCase', 'UPPER_CASE'],
          leadingUnderscore: 'allow',
          filter: {
            regex: String.raw`^_|[- /?:{}@%]|Provider|Mui|Comp|^item$|^condition$|^container$|^Container$|^\d+$`, // ignore properties with dashes/slashes/spaces
            match: false,
          },
        },
        // Variable-like naming rules
        {
          selector: 'variableLike',
          format: ['camelCase', 'UPPER_CASE'],
          trailingUnderscore: 'allow',
          filter: {
            regex: '^_|Comp|Container|Provider|Stack|Root',
            match: false,
          },
        },
        // Function variable naming rules
        {
          selector: 'variable',
          format: ['camelCase', 'PascalCase'],
          types: ['function'],
          filter: {
            regex: '^_|Comp|Provider|Stack', // allowing for 'Component' parameter names
            match: false,
          },
        },
        // Type naming rules
        {
          selector: 'typeLike',
          format: ['PascalCase'],
          filter: {
            regex: '^_|_$',
            match: false,
          },
        },
        // Boolean naming rules
        {
          selector: ['variable', 'property', 'parameter', 'typeProperty'],
          types: ['boolean'],
          format: ['UPPER_CASE', 'PascalCase'], // must be PascalCase because prefix is trimmed
          prefix: booleanNamePrefixes,
          filter: {
            regex: booleanNameExceptions,
            match: false,
          },
        },
      ],

      // TypeScript type safety rules
      '@typescript-eslint/no-explicit-any': ['warn', {fixToUnknown: true}],
      '@typescript-eslint/no-inferrable-types': ['warn', {ignoreParameters: true}],
      '@typescript-eslint/no-shadow': 'error',
      '@typescript-eslint/no-unsafe-enum-comparison': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-empty-function': 'warn',
      '@typescript-eslint/no-base-to-string': 'off',

      // TypeScript nullish handling
      '@typescript-eslint/prefer-nullish-coalescing': ['warn', {ignorePrimitives: {string: true}}],
      '@typescript-eslint/no-non-null-asserted-nullish-coalescing': 'error',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'error',
      '@typescript-eslint/no-extra-non-null-assertion': 'error',
      '@typescript-eslint/no-confusing-non-null-assertion': 'error',

      // TypeScript comments and annotations
      '@typescript-eslint/ban-ts-comment': [
        'error',
        {
          'ts-expect-error': 'allow-with-description',
          'ts-ignore': 'allow-with-description',
        },
      ],

      // TypeScript type structure rules
      '@typescript-eslint/no-empty-object-type': 'warn',
      '@typescript-eslint/no-unsafe-function-type': 'warn',
      '@typescript-eslint/no-wrapper-object-types': 'warn',
      '@typescript-eslint/array-type': ['warn', {default: 'array'}],
      '@stylistic/array-element-newline': ['warn', {multiline: true, consistent: true}],
      '@stylistic/array-bracket-newline': ['warn', 'consistent'],
      // '@stylistic/function-paren-newline': ['warn', 'multiline-arguments'], // 'consistent'

      // TODO: turn rule back on when these issues are resolved:
      // TODO:   https://github.com/eslint-stylistic/eslint-stylistic/issues/791
      // TODO:   https://github.com/eslint-stylistic/eslint-stylistic/issues/664
      // '@stylistic/no-extra-parens': ['warn',
      //   'all',
      //   {
      //     nestedBinaryExpressions: false,
      //     ignoreJSX: 'all',
      //     ternaryOperandBinaryExpressions: false,
      //     enforceForArrowConditionals: false,
      //     nestedConditionalExpressions: false,
      //   }],
      '@stylistic/no-mixed-operators': ['warn', {allowSamePrecedence: true}],
      '@stylistic/rest-spread-spacing': ['warn', 'never'],
      '@typescript-eslint/no-unnecessary-condition': 'warn',
      '@typescript-eslint/no-unnecessary-boolean-literal-compare': 'warn',
      '@typescript-eslint/no-unnecessary-type-arguments': 'off',

      // TypeScript consistency rules
      '@typescript-eslint/consistent-indexed-object-style': ['error', 'record'],
      '@typescript-eslint/consistent-type-assertions': ['error', {assertionStyle: 'as'}],
      '@typescript-eslint/consistent-type-definitions': ['error', 'type'],
      '@typescript-eslint/consistent-type-exports': 'error',
      '@typescript-eslint/consistent-type-imports': ['error', {prefer: 'type-imports'}],
      '@stylistic/member-delimiter-style': 'error',
      '@typescript-eslint/method-signature-style': 'error',

      // TypeScript expression rules
      '@typescript-eslint/no-confusing-void-expression': ['error', {ignoreArrowShorthand: true}],
      '@typescript-eslint/no-invalid-void-type': 'off', // Keep off per your comment
      '@typescript-eslint/no-meaningless-void-operator': 'error',
      '@typescript-eslint/no-misused-promises': 'off', // Consider enabling with proper options if needed
      '@typescript-eslint/no-var-requires': 'off', // Keep off per your comment

      // TypeScript modern features preference
      '@typescript-eslint/prefer-includes': 'error',
      '@typescript-eslint/prefer-optional-chain': 'error',
      '@typescript-eslint/prefer-reduce-type-parameter': 'error',
      '@typescript-eslint/prefer-string-starts-ends-with': 'error',

      // TypeScript stylistic rules
      '@stylistic/type-annotation-spacing': 'error',

      // Disabled TypeScript rules
      '@typescript-eslint/unbound-method': 'off', // Consider enabling if needed
      '@typescript-eslint/no-empty-interface': 'off',
      '@typescript-eslint/no-dynamic-delete': 'off',
      // --- Comment Formatting Rules ---
      'spaced-comment': 'off',
      '@stylistic/spaced-comment': ['warn', 'always', {line: {markers: ['!', '?', '-', '**']}}],

      // --- Unused Variables & Imports Rules ---
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'sonarjs/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      // --- Import Restrictions Rules ---
      'no-restricted-imports': 'off', // Use TS version
      '@typescript-eslint/no-restricted-imports': [
        'warn',
        {
          // Specific module import restrictions
          paths: [
            // React import restrictions
            {
              name: 'react',
              importNames: ['useContext'],
              message:
                'Use custom hooks that use `react-tracked`\'s `useTrackedState` instead.',
            },
            {
              name: 'react',
              importNames: ['createContext'],
              message: 'Import `createContainer` from `react-tracked` instead.',
            },
            {
              name: 'react',
              importNames: ['default'],
              message:
                'Import the named modules directly, or if using React Types, just use the global React type and don\'t import the module directly.',
            },
            // HTTP client restrictions
            {
              name: 'axios',
              message:
                'You should not need to import axios. Use the `createClient` method for using axios methods. Modify and extend `createClient` itself if you need more functionality.',
              allowTypeImports: true,
            },
            // State management restrictions
            {
              name: 'react-tracked',
              importNames: ['createContainer'],
              message: 'Import from `@utils` instead.',
            },
            // Utility library restrictions
            {
              name: 'memoize-one',
              message:
                'Import memoization functions `memoize` and `memoComponent` from `@utils` instead.',
            },
          ],
          // Import pattern restrictions
          patterns: [
            {
              group: ['src/*'],
              message:
                'Use absolute imports from a `@module` or a relative import instead.',
            },
            {
              group: ['types/*'],
              message: 'Use @types or relative importing instead',
            },
          ],
        },
      ],

      // --- Accessibility Rules ---
      // jsx-a11y rules
      'jsx-a11y/no-autofocus': 'off',

      // React Native a11y rules
      'react-native-a11y/has-valid-accessibility-ignores-invert-colors': 'off',
      'react-native-a11y/has-valid-accessibility-descriptors': 'off',

      // --- Banned API Rules ---
      'ban/ban': [
        'warn',
        {
          name: ['*', 'concat'],
          message:
            'Imperative operation: prefer use ES6 spread, i.e. [...items, newItem]',
        },
        {
          name: ['Object', 'assign'],
          message: 'Use the spread operator `{...obj}` instead',
        },
      ],

      // --- Code Structure Restrictions ---
      // No loops rule
      'no-loops/no-loops': 'error',

      // Syntax restrictions
      'no-restricted-syntax': [
        'error',
        {
          selector: 'SwitchStatement',
          message:
            'Switch statements are banned. Use object literal mapping instead.',
        },
        {
          selector:
            'CallExpression[callee.property.name="up"][arguments.0.value="xs"]',
          message:
            'All top-level CSS properties are implicitly `theme.breakpoints.up(\'xs\')` due to our mobile-first styling convention.',
        },
        {
          selector: String.raw`Literal[value=/^\d+\.?\d*(px|rem)$/]`,
          message:
            'Using pixel/rem values is banned. Use `theme.spacing()` instead. If *aboslutely necessary*, disable the rule for the line and give a comment why.',
        },
        {
          selector:
            'Property[key.name=/^justifyContent$/][value.type=Literal][value.value=/^left$/]',
          message: 'Did you mean to use `flex-start`?',
        },
        {
          selector:
            'NewExpression[callee.name=/Array|Object|RegExp|String|Number|Bollean/]',
          message:
            'New expressions are banned. You should never need to create a new object. Use object/array literals instead',
        },
      ],

      // --- ESLint Comments Rules ---
      '@eslint-community/eslint-comments/require-description': ['error', {ignore: ['eslint-enable']}],

      // --- React Rules ---
      // General React rules
      'react/style-prop-object': 'off', // Keep off for RN
      'react/prop-types': 'off',
      'react/react-in-jsx-scope': 'off', // Not needed with new JSX transform
      'react/no-multi-comp': ['error', {ignoreStateless: true}],
      'react/no-array-index-key': 'warn',
      'react/void-dom-elements-no-children': 'error',
      'react/destructuring-assignment': ['warn', 'always'],
      'react/boolean-prop-naming': ['warn', {rule: booleanNameConvention}],

      // Component definition rules
      'react/function-component-definition': ['error', {namedComponents: 'arrow-function'}],
      'react/jsx-filename-extension': ['error', {extensions: ['.tsx']}],
      'react/self-closing-comp': 'warn',

      // JSX formatting rules
      'react/jsx-curly-brace-presence': 'warn',
      'react/jsx-closing-bracket-location': ['warn', 'line-aligned'],
      'react/jsx-no-useless-fragment': ['warn', {allowExpressions: true}],
      'react/jsx-first-prop-new-line': ['warn'],
      'react/jsx-tag-spacing': ['warn', {beforeClosing: 'never'}],
      'react/jsx-key': 'error',
      'react/jsx-max-depth': ['error', {max: 6}],
      'react/jsx-no-comment-textnodes': 'warn',
      'react/jsx-boolean-value': ['error', 'never'],
      'react/jsx-props-no-multi-spaces': 'error',

      // JSX multiline formatting
      '@stylistic/multiline-ternary': ['warn', 'always-multiline', {ignoreJSX: true}],
      '@stylistic/jsx-wrap-multilines': [
        'warn',
        {
          declaration: 'parens-new-line',
          assignment: 'parens-new-line',
          return: 'parens-new-line',
          arrow: 'parens-new-line',
          condition: 'ignore',
          logical: 'ignore',
          prop: 'ignore',
        },
      ],

      // Props handling rules
      'react/jsx-sort-props': ['warn', {shorthandFirst: true, callbacksLast: true, reservedFirst: true}],
      'react/jsx-props-no-spreading': [
        'error',
        {
          exceptions: [
            'ScrollView',
            'Component',
            'Container',
            'Screen',
            'AppProvider',
            'Checkbox.Item',
            'TextInputPaper',
            'StatusIcon',
            'CircularProgressBase',
            'CircularProgress',
          ],
        },
      ],

      // Component props restrictions
      'react/forbid-component-props': ['warn',
        {
          forbid: [
          // Margin shorthand enforcement
            {propName: 'margin', message: 'Use `m` shorthand instead.'},
            {propName: 'marginRight', message: 'Use `mr` shorthand instead.'},
            {propName: 'marginLeft', message: 'Use `ml` shorthand instead.'},
            {propName: 'marginTop', message: 'Use `mt` shorthand instead.'},
            {propName: 'marginBottom', message: 'Use `mb` shorthand instead.'},
            // Padding shorthand enforcement
            {propName: 'padding', message: 'Use `p` shorthand instead.'},
            {propName: 'paddingRight', message: 'Use `pr` shorthand instead.'},
            {propName: 'paddingLeft', message: 'Use `pl` shorthand instead.'},
            {propName: 'paddingTop', message: 'Use `pt` shorthand instead.'},
            {propName: 'paddingBottom', message: 'Use `pb` shorthand instead.'},
          ],
        }],

      // --- React Hooks Rules ---
      'react-hooks-addons/no-unused-deps': 'warn',
      'react-hooks/exhaustive-deps': [
        'warn',
        {
          additionalHooks:
            '(useEffectIgnoreFirstRender|useAsyncEffect|useEffectDebounce|useEffectIgnoreFirstRender|useEffectInterval|useMemoCache|useMemoCacheKey|useMemoTiming|useCallbackThrottle)',
        },
      ],

      // --- Testing Library Rules ---
      'testing-library/no-render-in-lifecycle': ['error', {allowTestingFrameworkSetupHook: 'beforeEach'}],
      'testing-library/no-unnecessary-act': 'off',

      // --- Import Rules ---
      // Commented out rules kept for reference
      // 'import/no-default-export': 'warn',
      // 'import/no-restricted-paths': ['warn', { zones: restrictedPaths }], // Define restrictedPaths
      'import-x/no-named-as-default-member': 'off',
      'import-x/no-absolute-path': 'warn',
      'import-x/newline-after-import': 'warn',
      'import-x/no-cycle': ['error', {maxDepth: 1, ignoreExternal: true}],
      'import-x/order': ['warn',
        {
          groups: [
            'builtin', // Node.js built-in modules
            'external', // Packages from node_modules
            ['type', 'internal'], // Type imports, Absolute imports (often aliased like 'src/components')
            'parent', // Relative imports from parent directories (../)
            'sibling', // Relative imports from sibling directories (./)
            'index', // Index file imports (./index.js)
            'object', // Imports from object notation
          ],
        }],

      // --- Sort Rules ---
      'sort/imports': 'off', // use 'import-x/order' rule instead
      'sort/object-properties': 'off',
      'sort/type-properties': 'warn',
      'sort/string-unions': 'off',

      // --- Unicorn Rules ---
      'unicorn/prefer-global-this': 'off',
      'unicorn/prevent-abbreviations': 'off',
      'unicorn/no-array-reduce': 'off',
      'unicorn/no-array-for-each': 'off',
      'unicorn/prefer-top-level-await': 'off',
      'unicorn/no-array-callback-reference': 'off',
      'unicorn/prefer-switch': 'off',
      'unicorn/no-abusive-eslint-disable': 'off',
      'unicorn/prefer-object-from-entries': 'off',
      'unicorn/no-null': 'off',
      'unicorn/filename-case': [
        'warn',
        {
          cases: {camelCase: true, kebabCase: true},
          ignore: [/\.(js|cjs)$/], // Keep ignoring JS/CJS if needed
        },
      ],
      'unicorn/no-unused-properties': 'warn',
      'unicorn/consistent-destructuring': 'warn',
      'unicorn/no-useless-undefined': ['warn', {checkArguments: false}],

      // --- SonarJS Rules ---
      'sonarjs/no-duplicated-branches': 'warn',
      'sonarjs/no-duplicate-string': 'off',
      'sonarjs/deprecation': 'off',
      'sonarjs/cognitive-complexity': 'off', // use 'complexity' rule instead
      'sonarjs/no-nested-functions': 'off', // use 'complexity' rule instead
      'sonarjs/function-return-type': 'off', // use '@typescript-eslint/explicit-module-boundary-types' rule instead
      'sonarjs/no-redundant-optional': 'off', // use '@typescript-eslint/no-redundant-optional' rule instead
      'sonarjs/no-commented-code': 'off', // slow rule
      'sonarjs/todo-tag': 'off', // eventually re-enable
      'sonarjs/pseudo-random': 'off',
      'sonarjs/different-types-comparison': 'off', // too many false positives
      'sonarjs/redundant-type-aliases': 'off',
      'sonarjs/void-use': 'off', // allow to annotate async functions not explicitly awaited
      'sonarjs/no-nested-conditional': 'off', // use 'no-nested-ternary' rule instead
    },
  },

  // --- Overrides ---
  // Each override becomes a separate configuration object with a `files` property

  // --- Configuration Files Overrides ---
  {
    files: [
      './scripts/jestSetup.cjs',
      './functions/swagger-docs/index.js',
    ],
    rules: {
      'max-len': 'off',
      'max-lines-per-function': 'off',
      'ban/ban': 'off',
      'no-restricted-syntax': 'off',
      'no-console': 'off',
      'import-x/no-default-export': 'off',
      'import-x/no-restricted-paths': 'off',
      'unicorn/no-anonymous-default-export': 'off',
      'unicorn/no-process-exit': 'off',
      'unicorn/better-regex': 'off',
      'unicorn/prefer-module': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/require-await': 'off',
      '@typescript-eslint/dot-notation': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      'require-await': 'off',
    },
  },

  // --- Specific Config File Overrides ---
  {
    files: [
      './eslint.config.mjs',
      '**/vite.config.ts',
      '**/esbuild.config.js',
      '**/esbuild.cli.config.js',
      '**/metro.config.cjs',
      './jest.config.cjs',
      '**/babel.config.cjs',
      '**/app.config.ts',
      '**/app.config.cjs',
    ],
    rules: {
      'no-magic-numbers': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      'import-x/default': 'off',
    },
  },

  // --- Testing Files Overrides ---
  {
    files: [
      '**/*.test.{tsx,ts}',
      '**/*.test-exclude.ts',
      '**/testingHelpers.ts',
      'jestSetup.cjs',
    ],
    rules: {
      'max-lines-per-function': 'off',
      'max-lines': 'off',
      'no-console': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-restricted-imports': 'off',
      '@typescript-eslint/dot-notation': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      'unicorn/consistent-function-scoping': 'off',
      'unicorn/prefer-module': 'off',
    },
  },

  // --- JSX Files Overrides ---
  {
    files: ['**/*.tsx'],
    rules: {
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/no-use-before-define': 'off',
      'unicorn/filename-case': [
        'warn',
        {
          cases: {pascalCase: true},
          ignore: [/helper|example|test|drawer|make/i],
        },
      ],
      'padding-line-between-statements': [
        'warn',
        {blankLine: 'always', prev: '*', next: 'return'},
      ],
    },
  },

  // --- Types Folder Overrides ---
  {
    files: ['**/types/**/*.ts'],
    rules: {
      'sort/type-properties': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
    },
  },

  // --- Hooks, Constants, and Contexts Folder Overrides ---
  {
    files: ['**/hooks/**/*.ts', '**/constants/**/*.ts', '**/contexts/**/*.ts'],
    rules: {'@typescript-eslint/no-magic-numbers': 'off'},
  },

  // --- Utils Folder Overrides ---
  {
    files: ['**/utils/**/*.ts'],
    rules: {
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
    },
  },

  // --- Base Components and Assets Folders Overrides ---
  {
    files: [
      './src/base-components/**/*.tsx',
      './src/assets/**/*.tsx',
      './functions/emails/**/*.tsx',
    ],
    rules: {
      'react/destructuring-assignment': 'off',
      'react/jsx-props-no-spreading': 'off',
      'eslint-comments/no-unlimited-disable': 'off',
    },
  },

  // --- Functions Folder Overrides ---
  {
    files: ['./functions/**/*.ts'],
    rules: {
      'max-lines-per-function': 'off',
      'expo/no-env-var-destructuring': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/dot-notation': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
    },
  },

  // --- Report Generation Overrides ---
  {
    files: [
      './functions/report-generation/**/*.ts',
      './functions/report-generation/**/*.tsx',
    ],
    rules: {
      'react/jsx-max-depth': 'off',
      'react/forbid-component-props': 'off',
      'react/jsx-curly-brace-presence': 'off',
      '@typescript-eslint/no-restricted-imports': 'off',
      '@typescript-eslint/dot-notation': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      'no-console': 'off',
    },
  },

  // --- Apps Scripts Overrides ---
  {
    files: ['**/*.appsScript.mjs'],
    rules: {
      'func-style': 'off',
      'camelcase': 'off',
      'unused-imports/no-unused-vars': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/dot-notation': 'off',
    },
  },

  // --- ZX and Script Files Overrides ---
  {
    files: ['**/*.zx.mjs', './scripts/**/*.cjs'],
    languageOptions: {
      globals: {
        $: 'readonly',
        cd: 'readonly',
        argv: 'readonly',
        chalk: 'readonly',
        fs: 'readonly',
        path: 'readonly',
        question: 'readonly',
        sleep: 'readonly',
        ...globals.node,
      },
    },
    rules: {
      'max-lines-per-function': 'off',
      'complexity': 'off',
      'no-loops/no-loops': 'off',
      'max-len': 'off',
      'no-undef': 'off',
      'no-console': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unnecessary-condition': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/dot-notation': 'off',
      'ban/ban': 'off',
      'unicorn/consistent-function-scoping': 'off',
      'unicorn/prefer-spread': 'off',
      'unicorn/no-await-expression-member': 'off',
      'expo/no-env-var-destructuring': 'off',
      'import-x/no-unresolved': 'off', // Allow imports of config files that may not exist in CI
      'unicorn/prefer-module': 'off',
    },
  },

  // --- Email Template Files Overrides ---
  {
    files: ['./functions/emails/**/*.tsx'],
    rules: {
      'import-x/no-default-export': 'off',
      'no-restricted-syntax': 'off',
      'max-lines-per-function': 'off',
      'react/no-unescaped-entities': 'off',
      'react/jsx-max-depth': 'off',
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/naming-convention': 'off',
    },
  },

  // --- Page Components Overrides ---
  {
    files: ['./src/pages/**/*Page.tsx', './src/BottomStackNavigator.tsx'],
    rules: {'react/no-children-prop': 'off'},
  },

  // --- API Files Overrides ---
  {
    files: ['**/api/**/*.ts'],
    rules: {'@typescript-eslint/naming-convention': 'off'},
  },

  // --- Web Sign Up Project Overrides ---
  {
    files: ['./web-sign-up/**/*.ts', './web-sign-up/**/*.tsx'],
    rules: {'no-console': 'off', '@typescript-eslint/no-magic-numbers': 'off'},
  },
);
/* eslint-enable max-lines */
