import {Box, Text} from '@base-components';
import {useAppUserSafe, useTrackingDeviceType} from '@contexts';
import {getTrackingDeviceTypeDisplayName, timestampToDate} from '@types';
import {formatDateWeekdayMonthDay} from '@utils';

export const LastIntegrationSwitchInfo: React.FC = () => {
  const appUser = useAppUserSafe();
  const currentDeviceType = useTrackingDeviceType();
  const lastSwitch = appUser.lastHealthIntegrationSwitch;

  if (!lastSwitch) return null;

  const switchDate = timestampToDate(lastSwitch.switchDate);
  const formattedDate = formatDateWeekdayMonthDay(switchDate);

  return (
    <Box pt={1}>
      <Text variant='bodySmall' style={{fontStyle: 'italic'}}>
        Last integration switch: {formattedDate}
        {lastSwitch.previousDeviceType && currentDeviceType && (
          <>
            {' '}
            (from {getTrackingDeviceTypeDisplayName(lastSwitch.previousDeviceType)} to{' '}
            {getTrackingDeviceTypeDisplayName(currentDeviceType)})
          </>
        )}
      </Text>
    </Box>
  );
};
