import {useEffect, useState} from 'react';
import {Box, ButtonContained, ButtonOutlined, Icon, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAddSnack, useFirstSyncDate, useHasAnyHealthPermissions, useSyncHealthStatsFullReload} from '@contexts';
import {ManagedModal} from '../Shared';
import {DateAndTimeComponent} from '../DateAndTimeComponent';

type FullHealthSyncReloadProps = {
  enableOnMount?: boolean;
  hideUi?: boolean;
  onIsPendingChange?: (isPending: boolean) => void;
};

export const FullHealthSyncReload: React.FC<FullHealthSyncReloadProps> = ({
  enableOnMount = false,
  hideUi = false,
  onIsPendingChange,
}) => {
  const hasAnySyncEnabled = useHasAnyHealthPermissions();
  const {firstSyncDate, setFirstSyncDate} = useFirstSyncDate();
  const {isPending: isHealthStatsLoading, sync} = useSyncHealthStatsFullReload(firstSyncDate);
  const [hasCalledSync, setHasCalledSync] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const snack = useAddSnack();

  useEffect(() => {
    onIsPendingChange?.(isHealthStatsLoading);
  }, [isHealthStatsLoading, onIsPendingChange]);

  useEffect(() => {
    if (!enableOnMount || isHealthStatsLoading || hasCalledSync) return;

    void sync();
    setHasCalledSync(true);
    // eslint-disable-next-line react-compiler/react-compiler -- intentional
    // eslint-disable-next-line react-hooks/exhaustive-deps -- sync only on mount
  }, [enableOnMount, isHealthStatsLoading, hasCalledSync]);

  if (!hasAnySyncEnabled || hideUi) return null;

  return (
    <>
      <ButtonOutlined
        disabled={isHealthStatsLoading}
        icon='refresh'
        loading={isHealthStatsLoading}
        mt={2}
        onPress={() => setIsModalOpen(true)}
      >
        {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_BUTTON_LABEL}
      </ButtonOutlined>
      <ManagedModal
        isNewBackgroundColor
        isOpen={isModalOpen}
        onDismiss={() => setIsModalOpen(false)}
      >
        <Text pt={1} variant='labelLarge'>
          <Icon name='refresh' size={18} />{' '}
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_MODAL_TITLE}
        </Text>
        <Text pt={2} variant='bodyMedium'>
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_EXPLANATION}
        </Text>

        <Text pb={1} pt={2} variant='bodyMedium'>
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_DATE_LABEL}
        </Text>
        <DateAndTimeComponent
          type='date'
          value={firstSyncDate}
          onChange={setFirstSyncDate}
        />

        <Box pt={2} />
        <ButtonContained
          disabled={isHealthStatsLoading}
          loading={isHealthStatsLoading}
          onPress={async () => {
            await sync();
            setIsModalOpen(false);
            snack(CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_COMPLETE_SNACK);
          }}
        >
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_BUTTON_CONTINUE}
        </ButtonContained>

        <Box pt={1} />
        <ButtonOutlined
          disabled={isHealthStatsLoading}
          icon='cancel'
          onPress={() => setIsModalOpen(false)}
        >
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEALTH_SYNC_CANCEL}
        </ButtonOutlined>
      </ManagedModal>
    </>
  );
};
