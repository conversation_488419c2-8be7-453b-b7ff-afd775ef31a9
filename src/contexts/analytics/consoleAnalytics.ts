import {analyticsConsoleLog} from '@utils';
import type {AnalyticsService} from './analyticsService';

export const consoleAnalytics = (): AnalyticsService => ({
  logEvent: async (name, params) => {
    analyticsConsoleLog(`[event] ${name}`, params);
  },

  setCurrentScreen: async screenName => {
    analyticsConsoleLog(`[screen] ${screenName}`);
  },

  setUserId: async id => {
    // eslint-disable-next-line no-console -- analytics
    console.log('[Analytics][userId]', id);
  },

  setUserProperties: async props => {
    // eslint-disable-next-line no-console -- analytics
    console.log('[Analytics][userProperties]', props);
  },
});
