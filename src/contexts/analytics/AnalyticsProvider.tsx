// eslint-disable-next-line @typescript-eslint/no-restricted-imports -- for analytics context
import {createContext, type ReactNode, useContext, useEffect} from 'react';
import {isProd} from '@constants';
import {formatUserProperties, getDeviceState} from '@utils';
import type {AnalyticsService} from './analyticsService';
import {useAppUser} from '../authContext';
import {consoleAnalytics} from './consoleAnalytics';
import {firebaseAnalyticsService} from './firebaseAnalytics';

// Default to console analytics
const defaultService = consoleAnalytics();
// eslint-disable-next-line @typescript-eslint/naming-convention -- context naming convention
const AnalyticsContext = createContext<AnalyticsService>(defaultService);

export const useAnalytics = () => useContext(AnalyticsContext);

export const AnalyticsProvider: React.FC<{children: ReactNode}> = ({children}) => {
  // This can be changed to determine provider from Expo config extra.analyticsProvider
  const service: AnalyticsService = isProd ? firebaseAnalyticsService() : defaultService;
  const appUser = useAppUser();

  useEffect(() => {
    if (!appUser) return;

    const device = getDeviceState();
    void service.setUserId(appUser.id);
    void service.setUserProperties(formatUserProperties(appUser, device));
  }, [appUser, service]);

  return <AnalyticsContext.Provider value={service}>{children}</AnalyticsContext.Provider>;
};
