import {useMutation} from '@tanstack/react-query';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {DOMAIN_CONSTANTS} from '@constants';
import {useInvalidateClientSummary} from '@data-hooks';
import {
  useCurrentDateInterval,
  useCurrentTimeZoneWithDefault,
  useDateEveryHourChanged,
  useMutationCooldown,
} from '@hooks';
import {
  type AppUserHealthData,
  type DateInterval,
  dateToRawTimestamp,
  getIsoMonthStringFromDate,
  type HealthData,
  type HealthStats,
  timestampToDate,
  type WeightSample,
} from '@types';
import {
  formatHealthSyncParams,
  getCurrentMileage,
  getIsChangeInWeightSamples,
  healthStatsDatesGroupedByDay,
  healthSyncLog as log,
  healthSyncWarn as warn,
  isEmptyArray,
  isNonEmptyArray,
  mergeWeightSamplesWithOverride,
  onHapticSuccess,
  weightSampleToFirebaseTimestamps,
} from '@utils';
import {useAppUserHealthDataOnly, useGetHealthSyncStartDate} from './authContext';
import {
  getHealthDataForMonthRange,
  useAppConfig,
  useAppUserPartialMutation,
  useHealthData,
  useUpdateHealthDataMutation,
} from './firestore';
import {
  useHasAllStepPermissions,
  useHasAllWeightPermissions,
  useHasAnyHealthPermissions,
} from './healthSync';
import {getHealthStats, getWeightSamples} from './healthSyncHelpers';
import {useAnalytics} from './analytics';

const getHealthStatsFromBothSystems = (
  appUser: AppUserHealthData,
  newHealthData: HealthData[],
  interval: DateInterval,
): HealthStats[] => {
  const filterByInterval = (stats: HealthStats[]) =>
    stats.filter(
      s =>
        timestampToDate(s.startDate) >= interval.startDate &&
        timestampToDate(s.endDate) <= interval.endDate,
    );

  // If has data, return it from the new system, otherwise use old system
  return isNonEmptyArray(newHealthData)
    ? newHealthData.flatMap(d => filterByInterval(d.healthStats))
    : filterByInterval(appUser.healthDayStats);
};

const getWeightSamplesFromBothSystems = (
  appUser: AppUserHealthData,
  newHealthData: HealthData[],
  interval: DateInterval,
): WeightSample[] => {
  const filterByInterval = (samples: WeightSample[]) =>
    samples.filter(
      s =>
        timestampToDate(s.dateTime) >= interval.startDate &&
        timestampToDate(s.dateTime) <= interval.endDate,
    );

  // If has data, return it from the new system, otherwise use old system
  return isNonEmptyArray(newHealthData)
    ? newHealthData.flatMap(d => filterByInterval(d.weightSamples ?? []))
    : filterByInterval(appUser.weightSamples ?? []);
};

const getHealthDataFromBothSystems = (
  appUser: AppUserHealthData,
  newHealthData: HealthData[],
  interval: DateInterval,
) => {
  // Get the health stats from both systems filtered by the interval
  const healthStats = getHealthStatsFromBothSystems(appUser, newHealthData, interval);
  const weightSamples = getWeightSamplesFromBothSystems(appUser, newHealthData, interval);

  return {healthStats, weightSamples};
};

const getHealthDataFetchBothSystems = async (
  appUser: AppUserHealthData,
  interval: DateInterval,
) => {
  // Fetch new health data first
  const timeZone = appUser.timeZone ?? DOMAIN_CONSTANTS().DEFAULT_TIME_ZONE;
  const startMonth = getIsoMonthStringFromDate(interval.startDate, timeZone);
  const endMonth = getIsoMonthStringFromDate(interval.endDate, timeZone);
  const newHealthData = await getHealthDataForMonthRange(appUser.id, startMonth, endMonth);

  // Get the health stats from both systems filtered by the interval
  return getHealthDataFromBothSystems(appUser, newHealthData, interval);
};

const useSyncHealthData = (
  appUser: AppUserHealthData,
  options: {
    isFetchStats?: boolean;
    isFetchWeight?: boolean;
    isOverride?: boolean | undefined;
    startDate?: Date;
  } = {},
) => {
  const analytics = useAnalytics();
  const {mutateAsync: appUserPartialMutation} = useAppUserPartialMutation(appUser.id);
  const startDate = useGetHealthSyncStartDate(options.startDate);
  const invalidateClientSummary = useInvalidateClientSummary(appUser.id);
  const {mutateAsync: updateHealthData} = useUpdateHealthDataMutation(appUser.id);
  const mutationFn = useCallback(async () => {
    const timeZone = appUser.timeZone ?? DOMAIN_CONSTANTS().DEFAULT_TIME_ZONE;
    const stepLength = appUser.stepLength ?? DOMAIN_CONSTANTS().DEFAULT_STEP_LENGTH_INCHES;

    // Fetch health data in parallel
    const [healthStatsUpdates, weightSampleUpdates] = await Promise.all([
      options.isFetchStats
        ? getHealthStats(
            startDate,
            timeZone,
            stepLength,
            appUser.isMileageGpsSourced,
            appUser.fitbit,
          )
        : Promise.resolve(undefined),
      options.isFetchWeight ? getWeightSamples(startDate, timeZone) : Promise.resolve(undefined),
    ]);

    // Skip if no updates
    if (isEmptyArray(healthStatsUpdates) && isEmptyArray(weightSampleUpdates)) {
      log('HealthData', 'No new health data found, skipping syncing health data');
      return;
    }

    const startTime = Date.now();
    log(
      'HealthData',
      `Updating app user with new HealthData for ${startDate.toISOString()} to now`,
    );
    const existingHealthStats = await getHealthDataFetchBothSystems(appUser, {
      startDate,
      endDate: new Date(),
    });
    const endTime = Date.now();
    log('HealthData', `Fetched existing health data in ${endTime - startTime}ms`);

    const isChangeInWeightSamples =
      !!options.isOverride ||
      (weightSampleUpdates &&
        getIsChangeInWeightSamples(existingHealthStats.weightSamples, weightSampleUpdates));
    // Only merge weight samples if there are changes
    const mergedWeightSamples =
      isChangeInWeightSamples && weightSampleUpdates
        ? mergeWeightSamplesWithOverride(
            appUser.weightSamples,
            weightSampleUpdates,
            options.isOverride,
          )
        : undefined;

    // Persist the updates to the health data collection
    // MUST sync and wait for health data *first* to ensure data is updated for challenge
    // re-calculation triggered when updating appUser's lastHealthStatsSync date
    const {hasMadeUpdate} = await updateHealthData({
      newHealthStats: healthStatsUpdates,
      newWeightSamples: weightSampleUpdates,
      isOverride: !!options.isOverride,
    });

    const shouldUpdateUser = hasMadeUpdate || isChangeInWeightSamples;
    if (!shouldUpdateUser) {
      log('HealthData', 'No changes in health data or weight, skipping updating app user');
      return;
    }

    // Persist the updated app user with sync date and weight (weight is DEPRECATED)
    const partialAppUser = {
      ...(isNonEmptyArray(mergedWeightSamples) && {
        weightSamples: mergedWeightSamples.map(weightSampleToFirebaseTimestamps),
      }),
      lastHealthStatsSync: dateToRawTimestamp(new Date()),
    };

    // Continue updating the user document (backward compatibility)
    await appUserPartialMutation(partialAppUser);

    void analytics.logEvent('health_data_synced', formatHealthSyncParams(healthStatsUpdates));

    log('HealthData', 'Synced new HealthData data successfully');
    await invalidateClientSummary();
  }, [
    appUser,
    appUserPartialMutation,
    analytics,
    invalidateClientSummary,
    options.isFetchStats,
    options.isFetchWeight,
    options.isOverride,
    startDate,
    updateHealthData,
  ]);

  const syncMutation = useMutationCooldown(
    {
      mutationFn,
      mutationKey: ['syncHealthStatsMutation', appUser.id],
    },
    DOMAIN_CONSTANTS().DEBOUNCE.SYNC_HEALTH_STATS_MS,
    'HealthData',
  );

  return {
    sync: syncMutation.mutateAsync,
    isPending: syncMutation.isPending,
  };
};

export const useSyncRefreshAll = () => {
  const appUser = useAppUserHealthDataOnly();
  const isOverride = !!appUser.fitbit; // TEMPORARY: For Fitbit to fix override issue
  const hasAnyHealthSyncPermissions = useHasAnyHealthPermissions();
  const {data: hasWeightPermissions} = useHasAllWeightPermissions();
  const {data: hasStepPermissions} = useHasAllStepPermissions();
  const {sync: syncHealthData} = useSyncHealthData(appUser, {
    isOverride,
    isFetchStats: !!hasStepPermissions,
    isFetchWeight: !!hasWeightPermissions,
  });

  const mutationFn = useCallback(() => {
    if (!hasAnyHealthSyncPermissions) {
      warn('all', 'No health sync permissions found, skipping syncing health data');
      return Promise.resolve();
    }
    if (!!hasWeightPermissions || !!hasStepPermissions) {
      return syncHealthData();
    }

    warn('all', 'No health sync permissions found, skipping syncing health data');
    return Promise.resolve();
  }, [hasAnyHealthSyncPermissions, hasStepPermissions, hasWeightPermissions, syncHealthData]);

  const syncMutation = useMutationCooldown(
    {
      mutationFn,
      mutationKey: [
        'useSyncRefreshAll',
        hasAnyHealthSyncPermissions,
        hasStepPermissions,
        hasWeightPermissions,
      ],
    },
    DOMAIN_CONSTANTS().DEBOUNCE.SYNC_HEALTH_STATS_MS,
    'HealthData',
  );
  const sync = syncMutation.mutateAsync;

  // Run sync on app foreground
  // useOnAppStateForeground(syncMutation.mutateAsync, undefined, 'HomeScreen', MS_5_MINUTES);

  // Run sync on mount, but only if values loaded
  const hasRun = useRef(false); // Track if the effect has already run
  useEffect(() => {
    if (
      hasAnyHealthSyncPermissions === undefined ||
      hasWeightPermissions === undefined ||
      hasStepPermissions === undefined ||
      hasRun.current
    ) {
      return;
    }

    hasRun.current = true;
    void sync();
  }, [hasAnyHealthSyncPermissions, hasStepPermissions, hasWeightPermissions, sync]);

  // Build external refresh hook
  const onHapticRefresh = useCallback(async () => {
    await sync();
    await onHapticSuccess();
  }, [sync]);

  return {onHapticRefresh, isPending: syncMutation.isPending};
};

export const useFirstSyncDate = () => {
  const appUser = useAppUserHealthDataOnly();
  console.log('🔍 useFirstSyncDate debug - appUser.lastHealthIntegrationSwitch:', appUser.lastHealthIntegrationSwitch);
  const appConfig = useAppConfig();
  const currentDate = useDateEveryHourChanged();
  const accountCreatedDate = useMemo(
    () => timestampToDate(appUser.accountCreatedDateTime),
    [appUser.accountCreatedDateTime],
  );
  const initialSyncDate = useMemo(() => {
    // Use last integration switch date if available and more recent than account creation
    const switchDate = appUser.lastHealthIntegrationSwitch?.switchDate;
    console.log('🔍 initialSyncDate debug - switchDate raw:', switchDate);

    const switchDateTime = switchDate
      ? timestampToDate(switchDate)
      : undefined;

    const isMoreRecentThanAccount = switchDateTime && switchDateTime > accountCreatedDate;
    console.log('🔍 initialSyncDate debug - switchDateTime > accountCreatedDate:', isMoreRecentThanAccount);

    const baseStartDate = isMoreRecentThanAccount
      ? switchDateTime
      : accountCreatedDate;
    console.log('🔍 initialSyncDate debug - baseStartDate chosen:', baseStartDate);

    const isOlderMaxDays =
      currentDate.getTime() - baseStartDate.getTime() > appConfig.healthSyncMaxMsAgo;
    console.log('🔍 initialSyncDate debug - isOlderMaxDays:', isOlderMaxDays);
    console.log('🔍 initialSyncDate debug - healthSyncMaxMsAgo:', appConfig.healthSyncMaxMsAgo);

    const olderMaxDaysDate = new Date(currentDate.getTime() - appConfig.healthSyncMaxMsAgo);
    console.log('🔍 initialSyncDate debug - olderMaxDaysDate:', olderMaxDaysDate);

    const finalDate = isOlderMaxDays ? baseStartDate : olderMaxDaysDate;
    console.log('🔍 initialSyncDate debug - FINAL initialSyncDate:', finalDate);

    return finalDate;
  }, [
    accountCreatedDate,
    appConfig.healthSyncMaxMsAgo,
    currentDate,
    appUser.lastHealthIntegrationSwitch,
  ]);

  const [firstSyncDate, setFirstSyncDate] = useState(initialSyncDate);

  // Reset firstSyncDate whenever initialSyncDate changes (reactive to external changes)
  useEffect(() => {
    setFirstSyncDate(prevDate => {
      // Only update if the date has actually changed to prevent unnecessary re-renders
      if (prevDate.getTime() !== initialSyncDate.getTime()) {
        return initialSyncDate;
      }
      return prevDate;
    });
  }, [initialSyncDate]);

  const hasChanged = initialSyncDate.getTime() !== firstSyncDate.getTime();

  return {firstSyncDate, setFirstSyncDate, hasChanged, accountCreatedDate};
};

export const useSyncHealthStatsFullReload = (startSyncDate: Date) => {
  const appUser = useAppUserHealthDataOnly();
  return useSyncHealthData(appUser, {
    isFetchStats: true,
    startDate: startSyncDate,
    isOverride: true,
  });
};

export const useAppUserHealthDataByDay = (
  appUser: AppUserHealthData | undefined,
  interval: DateInterval,
) => {
  const timeZone = appUser?.timeZone ?? DOMAIN_CONSTANTS().DEFAULT_TIME_ZONE;
  const {data: healthDataNew} = useHealthData(appUser?.id, interval, timeZone);

  // return useQuery({
  //   queryKey: [
  //     'healthDataByDay',
  //     appUser.id,
  //     interval.startDate.toISOString(),
  //     interval.endDate.toISOString(),
  //     timeZone,
  //     `health-data-size-${healthDataNew?.length ?? 0}`,
  //   ],
  //   queryFn: () => {
  //     if (!healthDataNew) return null;

  //     log(
  //       'HealthData',
  //       isEmptyArray(healthDataNew)
  //         ? 'Using old health data system...'
  //         : 'Using new health data system...',
  //     );
  //     const healthDataBothSystems = getHealthDataFromBothSystems(appUser, healthDataNew, interval);
  //     return healthStatsDatesGroupedByDay(healthDataBothSystems.healthStats, timeZone);
  //   },
  //   enabled: !!healthDataNew,
  //   initialData: () => null,
  //   staleTime: Infinity, // Keep the data fresh until explicitly invalidated
  // }).data;
  return useMemo(() => {
    if (!healthDataNew || !appUser) return;

    log(
      'HealthData',
      isEmptyArray(healthDataNew)
        ? 'Using old health data system...'
        : 'Using new health data system...',
    );
    const healthDataBothSystems = getHealthDataFromBothSystems(appUser, healthDataNew, interval);
    return healthStatsDatesGroupedByDay(healthDataBothSystems.healthStats, timeZone);
  }, [appUser, healthDataNew, interval, timeZone]);
};

export const useGetCurrentMileageDWM = (healthStats: HealthStats[]) => {
  const dateInterval = useCurrentDateInterval();
  const timeZone = useCurrentTimeZoneWithDefault();

  return useMemo(() => {
    const mapOfStats = healthStatsDatesGroupedByDay(healthStats, timeZone);
    const stats = [...mapOfStats.values()];
    return getCurrentMileage(stats, dateInterval);
  }, [dateInterval, healthStats, timeZone]);
};

export const useSubmitManualEntry = (appUser: AppUserHealthData) => {
  const {mutateAsync: appUserPartialMutation} = useAppUserPartialMutation(appUser.id);
  const invalidateClientSummary = useInvalidateClientSummary(appUser.id);
  const {mutateAsync: updateHealthData} = useUpdateHealthDataMutation(appUser.id);

  const mutationFn = useCallback(
    async (options: {entry: HealthStats; isDeleteOverlapping?: boolean | undefined}) => {
      log('HealthData', 'Updating app user with manual health entry');

      // Persist the updates to the health data collection
      const {hasMadeUpdate} = await updateHealthData({
        newHealthStats: [options.entry],
        isDeleteOverlapping: options.isDeleteOverlapping,
      });

      if (!hasMadeUpdate) {
        log('HealthData', 'No changes detected after merging, skipping update');
        return;
      }

      // Update the sync timestamp
      const partialAppUser = {
        lastHealthStatsSync: dateToRawTimestamp(new Date()),
      };

      // Update the user document
      await appUserPartialMutation(partialAppUser);

      log('HealthData', 'Manual entry saved successfully');
      await invalidateClientSummary();
    },
    [appUserPartialMutation, invalidateClientSummary, updateHealthData],
  );

  return useMutation({
    mutationFn,
    mutationKey: ['submitManualEntryMutation', appUser.id],
  });
};
