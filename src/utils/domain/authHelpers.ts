import {
  type AppUser,
  type AppUserHealthData,
  type EditAppUser,
  type PartialUndefined,
  type RawTimestamp,
  timestampToDate,
} from '@types';

export const getHealthSyncStartDate = (
  accountCreatedDateTime: RawTimestamp,
  lastHealthStatsSync: RawTimestamp | undefined,
  minMsAgo: number,
  currentDate: Date,
  lastHealthIntegrationSwitchDate?: RawTimestamp,
) => {
  const accountCreationDate = timestampToDate(accountCreatedDateTime);
  const switchDate = lastHealthIntegrationSwitchDate
    ? timestampToDate(lastHealthIntegrationSwitchDate)
    : undefined;

  // Use switch date as base if it exists and is more recent than account creation
  const baseStartDate = switchDate && switchDate > accountCreationDate
    ? switchDate
    : accountCreationDate;

  const minAllowedDate = new Date(currentDate.getTime() - minMsAgo);
  // eslint-disable-next-line unicorn/prefer-math-min-max -- date comparison
  const minMsAgoDate = baseStartDate >= minAllowedDate ? baseStartDate : minAllowedDate;
  const lastSyncDate = lastHealthStatsSync ? timestampToDate(lastHealthStatsSync) : undefined;
  // Return baseStartDate if it's within the last minMsAgo milliseconds, else minAllowedDate
  return lastSyncDate && lastSyncDate <= minMsAgoDate ? lastSyncDate : minMsAgoDate;
};

export const getAppUserWithoutHealthStats = <T extends PartialUndefined<AppUserHealthData>>(
  appUser: T,
) => {
  const {healthDayStats: _, ...rest} = appUser;
  return rest;
};

export const getAppUserHealthDataOnly = (appUser: AppUser): AppUserHealthData => ({
  id: appUser.id,
  healthDayStats: appUser.healthDayStats,
  accountCreatedDateTime: appUser.accountCreatedDateTime,
  ...(appUser.weightSamples && {weightSamples: appUser.weightSamples}),
  ...(appUser.fitbit && {fitbit: appUser.fitbit}),
  ...(appUser.isMileageGpsSourced && {isMileageGpsSourced: true}),
  ...(appUser.stepLength && {stepLength: appUser.stepLength}),
});

export const getEditAppUserOnly = (appUser: AppUser): EditAppUser => ({
  id: appUser.id,
  accountCreatedDateTime: appUser.accountCreatedDateTime,
  firstName: appUser.firstName,
  lastName: appUser.lastName,
  email: appUser.email,
  ...(appUser.timeZone && {timeZone: appUser.timeZone}),
  ...(appUser.phoneNumber && {phoneNumber: appUser.phoneNumber}),
  ...(appUser.goals && {goals: appUser.goals}),
  ...(appUser.profilePicture && {profilePicture: appUser.profilePicture}),
  ...(appUser.isMileageGpsSourced && {isMileageGpsSourced: true}),
  ...(appUser.heightInInches && {heightInInches: appUser.heightInInches}),
  ...(appUser.stepLength && {stepLength: appUser.stepLength}),
  ...(appUser.dailyGoalStepCount && {dailyGoalStepCount: appUser.dailyGoalStepCount}),
  ...(appUser.dailyGoalMileageInMeters && {
    dailyGoalMileageInMeters: appUser.dailyGoalMileageInMeters,
  }),
});
