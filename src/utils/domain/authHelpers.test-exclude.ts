import {dateToRawTimestamp} from '@types';
import {getHealthSyncStartDate} from './authHelpers';

const SEVEN_DAYS_IN_MS = 7 * 24 * 60 * 60 * 1000;

describe('auth helpers', () => {
  const appUser = {
    accountCreatedDateTime: dateToRawTimestamp(new Date(2024, 0, 1, 12)),
    lastHealthStatsSync: undefined,
  };

  it('should return the accountedCreatedDateTime if created 2 hours prior', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 1, 14);

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
    );

    // Assert
    const expected = new Date(2024, 0, 1, 12);
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });

  it('should return the accountedCreatedDateTime if less than 7 days', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 7, 12);

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
    );

    // Assert
    const expected = new Date(2024, 0, 1, 12);
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });

  it('should return the accountedCreatedDateTime if exactly 7 days', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 8, 12);

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
    );

    // Assert
    const expected = new Date(2024, 0, 1, 12);
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });

  it('should return exactly 7 days ago if older than accountedCreatedDateTime', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 10, 12);

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
    );

    // Assert
    const expected = new Date(2024, 0, 3, 12);
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });

  it('should use integration switch date when more recent than account creation', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 10, 12);
    const switchDate = dateToRawTimestamp(new Date(2024, 0, 5, 10)); // More recent than account creation

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
      switchDate,
    );

    // Assert
    const expected = new Date(2024, 0, 5, 10);
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });

  it('should use account creation date when integration switch date is older', () => {
    // Arrange
    const currentDate = new Date(2024, 0, 10, 12);
    const switchDate = dateToRawTimestamp(new Date(2023, 11, 30, 10)); // Older than account creation

    // Act
    const actual = getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      SEVEN_DAYS_IN_MS,
      currentDate,
      switchDate,
    );

    // Assert
    const expected = new Date(2024, 0, 3, 12); // 7 days ago from current date
    expect(actual.getTime()).toStrictEqual(expected.getTime());
  });
});
